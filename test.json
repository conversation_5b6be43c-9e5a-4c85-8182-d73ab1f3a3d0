{"jsonrpc": "2.0", "method": "textDocument/publishDiagnostics", "params": {"uri": "file:///test.py", "diagnostics": [{"source": "pyflakes", "range": {"start": {"line": 0, "character": 9}, "end": {"line": 0, "character": 19}}, "message": "invalid syntax", "severity": 1}, {"source": "pycodestyle", "range": {"start": {"line": 2, "character": 0}, "end": {"line": 2, "character": 100}}, "message": "E901 TokenError: EOF in multi-line statement", "code": "E901", "severity": 2}, {"source": "pycodestyle", "range": {"start": {"line": 1, "character": 8}, "end": {"line": 1, "character": 8}}, "message": "W292 no newline at end of file", "code": "W292", "severity": 2}, {"source": "pycodestyle", "range": {"start": {"line": 1, "character": 4}, "end": {"line": 1, "character": 8}}, "message": "E128 continuation line under-indented for visual indent", "code": "E128", "severity": 2}], "version": 1}}