use std::{convert::Infallible, process::Stdio, str::FromStr};
use std::collections::HashMap;
use tokio::sync::Mutex;

use futures_util::{
    future::{select, Either},
    stream, SinkExt, StreamExt,
};
use tokio::{fs, process::Command};
use url::Url;
use warp::{Filter, Rejection, Reply};

use crate::lsp;
use super::super::utils::code_handler::transform_diagnostics;

use super::with_context;

#[derive(Debug, Clone)]
pub struct Context {
    /// One or more commands to start a Language Server.
    pub commands: Vec<Vec<String>>,
    /// Write file on save.
    pub sync: bool,
    /// Remap relative `source://` to absolute `file://`.
    pub remap: bool,
    /// Project root.
    pub cwd: Url,
    /// Language server mapping (language_id -> command_index)
    pub language_servers: HashMap<String, usize>,
}

#[derive(<PERSON><PERSON>, Debug, serde::Deserialize)]
struct Query {
    /// The command name of the Language Server to start.
    /// If not specified, the first one is started.
    name: String,
}

fn with_optional_query() -> impl Filter<Extract = (Option<Query>,), Error = Infallible> + Clone {
    warp::query::<Query>()
        .map(Some)
        .or_else(|_| async { Ok::<(Option<Query>,), Infallible>((None,)) })
}

/// Handler for WebSocket connection.
pub fn handler(ctx: Context) -> impl Filter<Extract = impl Reply, Error = Rejection> + Clone {
    warp::path::end()
        .and(warp::ws())
        .and(with_context(ctx))
        .and(with_optional_query())
        .map(|ws: warp::ws::Ws, ctx, query| {
            ws.with_compression()
                .on_upgrade(move |socket| on_upgrade(socket, ctx, query))
        })
}

#[tracing::instrument(level = "debug", err, skip(msg))]
async fn maybe_write_text_document(msg: &lsp::Message) -> Result<(), std::io::Error> {
    if let lsp::Message::Notification(lsp::Notification::DidSave { params }) = msg {
        if let Some(text) = &params.text {
            let uri = &params.text_document.uri;
            if uri.scheme() == "file" {
                if let Ok(path) = uri.to_file_path() {
                    if let Some(parent) = path.parent() {
                        tracing::debug!("writing to {:?}", path);
                        fs::create_dir_all(parent).await?;
                        fs::write(&path, text.as_bytes()).await?;
                    }
                }
            }
        }
    }
    Ok(())
}

async fn on_upgrade(socket: warp::ws::WebSocket, ctx: Context, query: Option<Query>) {
    tracing::info!("connected");
    if let Err(err) = connected(socket, ctx, query).await {
        tracing::error!("connection error: {}", err);
    }
    tracing::info!("disconnected");
}

#[tracing::instrument(level = "debug", skip(ws, ctx), fields(remap = %ctx.remap, sync = %ctx.sync))]
async fn connected(
    ws: warp::ws::WebSocket,
    mut ctx: Context,
    query: Option<Query>,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Build language server mapping
    ctx.language_servers = build_language_server_mapping(&ctx.commands);
    
    // Initialize ALL servers upfront
    tracing::info!("Initializing {} language servers", ctx.commands.len());
    let mut servers = Vec::new();
    let mut server_sends = Vec::new();
    let mut server_recvs = Vec::new();
    
    // Start all servers
    for (index, command) in ctx.commands.iter().enumerate() {
        tracing::info!("Starting server {}: {}", index, command[0]);
        let mut server = Command::new(&command[0])
            .args(&command[1..])
            .stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .kill_on_drop(true)
            .spawn()?;
        
        let server_send = lsp::framed::writer(server.stdin.take().unwrap());
        let server_recv = lsp::framed::reader(server.stdout.take().unwrap());
        
        servers.push(server);
        server_sends.push(server_send);
        server_recvs.push(server_recv);
    }
    
    // Initialize all servers
    for (index, server_send) in server_sends.iter_mut().enumerate() {
        tracing::debug!("Initializing server {}", index);
        let init_msg = serde_json::json!({
            "jsonrpc": "2.0",
            "id": index,
            "method": "initialize",
            "params": {
                "processId": null,
                "rootUri": ctx.cwd.as_str(),
                "capabilities": {
                    "textDocument": {
                        "publishDiagnostics": {
                            "relatedInformation": false
                        }
                    }
                }
            }
        });
        let init_text = serde_json::to_string(&init_msg)?;
        server_send.send(init_text).await?;
    }
    
    // Wait for initialize responses and send initialized notifications
    for (index, server_recv) in server_recvs.iter_mut().enumerate() {
        if let Some(Ok(response)) = server_recv.next().await {
            tracing::debug!("Server {} initialized: {}", index, response);
            let initialized_msg = serde_json::json!({
                "jsonrpc": "2.0",
                "method": "initialized",
                "params": {}
            });
            let initialized_text = serde_json::to_string(&initialized_msg)?;
            server_sends[index].send(initialized_text).await?;
        }
    }
    
    tracing::info!("All servers initialized successfully");
    
    // Choose default active server
    let mut active_server_index = if let Some(query) = query {
        if let Some(index) = ctx.commands.iter().position(|v| v[0] == query.name) {
            index
        } else {
            tracing::warn!(
                "Unknown Language Server '{}', falling back to the default",
                query.name
            );
            0
        }
    } else {
        0
    };
    
    tracing::info!("Using server {} as default: {}", active_server_index, ctx.commands[active_server_index][0]);
    
    let (mut client_send, client_recv) = ws.split();
    let client_recv = client_recv
        .filter_map(filter_map_warp_ws_message)
        .chain(stream::once(async { Ok(Message::Done) }));
    
    let ticks = stream::unfold(
        tokio::time::interval(std::time::Duration::from_secs(30)),
        |mut interval| async move {
            interval.tick().await;
            Some((Ok(Message::Tick), interval))
        },
    );
    let mut client_recv = stream::select(client_recv, ticks).boxed();

    let mut client_msg = client_recv.next();
    let mut server_msg = Some(Box::pin(server_recvs[active_server_index].next()));
    let mut is_alive = true;

    loop {
        // If server_msg is None, it means we switched servers and need a new future
        if server_msg.is_none() {
            server_msg = Some(Box::pin(server_recvs[active_server_index].next()));
        }
        
        match select(client_msg, server_msg.take().unwrap()).await {
            // From Client
            Either::Left((from_client, p_server_msg)) => {
                match from_client {
                    Some(Ok(Message::Message(mut msg))) => {
                        // Handle didOpen messages and potentially switch servers
                        if let lsp::Message::Notification(lsp::Notification::DidOpen { params }) = &msg {
                            let uri = params.text_document.uri.to_string();
                            let text = params.text_document.text.clone();
                            let language_id = params.text_document.language_id.clone();
                            
                            tracing::debug!("DidOpen for {} with language {}", uri, language_id);
                            
                            // Cache file info
                            FILE_TEXT_CACHE.lock().await.insert(uri.clone(), text);
                            LANGUAGE_CACHE.lock().await.insert(uri, language_id.clone());
                            
                            // Check if we need to switch to a different server
                            if let Some(server_index) = get_server_for_language(&language_id, &ctx.language_servers) {
                                if server_index != active_server_index {
                                    tracing::info!("Switching from {} to {} for language {}", 
                                        ctx.commands[active_server_index][0], 
                                        ctx.commands[server_index][0], 
                                        language_id
                                    );
                                    active_server_index = server_index;
                                    // Signal that we need a new server message future
                                    server_msg = None;
                                } else {
                                    tracing::debug!("Already using correct server {} for language {}", ctx.commands[server_index][0], language_id);
                                    server_msg = Some(p_server_msg);
                                }
                            } else {
                                tracing::debug!("No specific server found for language {}, using current server {}", language_id, ctx.commands[active_server_index][0]);
                                server_msg = Some(p_server_msg);
                            }
                        } else {
                            server_msg = Some(p_server_msg);
                        }
                        
                        if ctx.remap {
                            lsp::ext::remap_relative_uri(&mut msg, &ctx.cwd)?;
                            tracing::debug!("remapped relative URI from client");
                        }
                        if ctx.sync {
                            maybe_write_text_document(&msg).await?;
                        }
                        
                        let text = serde_json::to_string(&msg)?;
                        tracing::debug!("-> server[{}] {}", active_server_index, text);
                        server_sends[active_server_index].send(text).await?;
                    }

                    Some(Ok(Message::Invalid(text))) => {
                        tracing::warn!("-> server[{}] {}", active_server_index, text);
                        server_sends[active_server_index].send(text).await?;
                    }

                    Some(Ok(Message::Close)) => {
                        tracing::info!("received Close message");
                    }

                    Some(Ok(Message::Tick)) => {
                        if !is_alive {
                            tracing::warn!("terminating unhealthy connection");
                            break;
                        }

                        is_alive = false;
                        tracing::debug!("pinging the client");
                        client_send.send(warp::ws::Message::ping(vec![])).await?;
                    }

                    Some(Ok(Message::Pong)) => {
                        tracing::debug!("received pong");
                        is_alive = true;
                    }

                    Some(Ok(Message::Done)) => {
                        tracing::info!("connection closed");
                        break;
                    }

                    Some(Err(err)) => {
                        tracing::error!("websocket error: {}", err);
                    }

                    None => {
                        unreachable!("should never yield None");
                    }
                }

                client_msg = client_recv.next();
                // server_msg is handled at the start of the loop
            }

            // From Server
            Either::Right((from_server, p_client_msg)) => {
                match from_server {
                    Some(Ok(text)) => {
                        // Check if this is a diagnostic message that needs transformation
                        if let Ok(msg) = lsp::Message::from_str(&text) {
                            if let lsp::Message::Notification(lsp::Notification::PublishDiagnostics { params }) = &msg {
                                tracing::debug!("Received diagnostics from server[{}] for {}", active_server_index, params.uri);
                                tracing::debug!("Raw diagnostics: {:?}", params.diagnostics);
                                let uri = params.uri.to_string();
                                let file_text = FILE_TEXT_CACHE.lock().await.get(&uri).cloned().unwrap_or_default();
                                let language_id = LANGUAGE_CACHE.lock().await.get(&uri).cloned();
                                let lines: Vec<&str> = file_text.lines().collect();
                                let diagnostics = transform_diagnostics(&params.diagnostics, &lines, language_id.as_deref());
                                let custom = serde_json::json!({ "diagnostics": diagnostics });
                                let custom_text = serde_json::to_string(&custom)?;
                                tracing::debug!("Sending transformed diagnostics: {}", custom_text);
                                client_send.send(warp::ws::Message::text(custom_text)).await?;
                            } else {
                                // Forward other LSP messages normally
                                tracing::debug!("<- server[{}] {}", active_server_index, text);
                                client_send.send(warp::ws::Message::text(text)).await?;
                            }
                        } else {
                            // Forward non-LSP messages normally
                            tracing::debug!("<- server[{}] {}", active_server_index, text);
                            client_send.send(warp::ws::Message::text(text)).await?;
                        }
                    }

                    Some(Err(err)) => {
                        tracing::error!("server[{}] error: {}", active_server_index, err);
                    }

                    None => {
                        tracing::error!("server[{}] process exited unexpectedly", active_server_index);
                        client_send.send(warp::ws::Message::close()).await?;
                        break;
                    }
                }

                client_msg = p_client_msg;
                server_msg = Some(Box::pin(server_recvs[active_server_index].next()));
            }
        }
    }

    Ok(())
}

// Type to describe a message from the client conveniently.
#[allow(clippy::large_enum_variant)]
#[allow(clippy::enum_variant_names)]
enum Message {
    // Valid LSP message
    Message(lsp::Message),
    // Invalid JSON
    Invalid(String),
    // Close message
    Close,
    // Ping the client to keep the connection alive.
    // Note that this is from the interval stream and not actually from client.
    Tick,
    // Client disconnected. Necessary because the combined stream is infinite.
    Done,
    // A reply for ping or heartbeat from client.
    Pong,
}

// Parse the message and ignore anything we don't care.
async fn filter_map_warp_ws_message(
    wsm: Result<warp::ws::Message, warp::Error>,
) -> Option<Result<Message, warp::Error>> {
    match wsm {
        Ok(msg) => {
            if msg.is_close() {
                Some(Ok(Message::Close))
            } else if msg.is_text() {
                let text = msg.to_str().expect("text");
                match lsp::Message::from_str(text) {
                    Ok(msg) => Some(Ok(Message::Message(msg))),
                    Err(_) => Some(Ok(Message::Invalid(text.to_owned()))),
                }
            } else if msg.is_pong() {
                Some(Ok(Message::Pong))
            } else {
                // Ignore any other message types
                None
            }
        }

        Err(err) => Some(Err(err)),
    }
}

// Global cache for file texts (uri -> text)
use lazy_static::lazy_static;

lazy_static! {
    static ref FILE_TEXT_CACHE: Mutex<HashMap<String, String>> = Mutex::new(HashMap::new());
    static ref LANGUAGE_CACHE: Mutex<HashMap<String, String>> = Mutex::new(HashMap::new());
}

/// Get server command index for a given language
fn get_server_for_language(language_id: &str, language_servers: &HashMap<String, usize>) -> Option<usize> {
    match language_id {
        "python" => language_servers.get("pylsp").copied()
            .or_else(|| language_servers.get("python-lsp-server").copied()),
        "c" | "cpp" | "c++" | "objective-c" | "objective-cpp" => 
            language_servers.get("clangd").copied(),
        "rust" => language_servers.get("rust-analyzer").copied(),
        "typescript" | "javascript" | "typescriptreact" | "javascriptreact" => 
            language_servers.get("typescript-language-server").copied(),
        "html" => language_servers.get("html-languageserver").copied(),
        "css" | "scss" | "less" => language_servers.get("css-languageserver").copied(),
        _ => None,
    }
}

/// Build language server mapping from commands
fn build_language_server_mapping(commands: &[Vec<String>]) -> HashMap<String, usize> {
    let mut mapping = HashMap::new();
    
    for (index, command) in commands.iter().enumerate() {
        if let Some(server_name) = command.first() {
            let key = match server_name.as_str() {
                "pylsp" | "python-lsp-server" => "pylsp",
                "clangd" => "clangd", 
                "rust-analyzer" => "rust-analyzer",
                "typescript-language-server" => "typescript-language-server",
                "html-languageserver" => "html-languageserver",
                "css-languageserver" => "css-languageserver",
                name => name, // Use the command name as-is for other servers
            };
            mapping.insert(key.to_string(), index);
        }
    }
    
    mapping
}