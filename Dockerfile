# Multi-stage build for lsp-ws-proxy
FROM rust:1.82-slim AS builder

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /usr/src/app

# Copy Cargo files first for better caching
COPY Cargo.toml ./

# Create a dummy main.rs to build dependencies
RUN mkdir src && echo "fn main() {}" > src/main.rs

# Build dependencies (this layer will be cached unless Cargo.toml changes)
RUN cargo build --release && rm -rf src/

# Copy source code
COPY src/ src/

# Build the actual application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies for Python and C/C++ LSP servers only
RUN apt-get update && apt-get install -y \
    # Basic utilities
    ca-certificates \
    curl \
    # Python LSP server dependencies
    python3 \
    python3-pip \
    # C/C++ LSP server
    clangd \
    && rm -rf /var/lib/apt/lists/*

# Install Python LSP server
RUN pip3 install --break-system-packages python-lsp-server[all]

# Create non-root user
RUN useradd -m -u 1000 lspproxy

# Copy the built binary from builder stage
COPY --from=builder /usr/src/app/target/release/lsp-ws-proxy /usr/local/bin/lsp-ws-proxy

# Create workspace directory
RUN mkdir -p /workspace && chown lspproxy:lspproxy /workspace

# Switch to non-root user
USER lspproxy

# Set working directory
WORKDIR /workspace

# Expose the default port
EXPOSE 9999

# Default command - only pylsp and clangd
CMD ["lsp-ws-proxy", "--listen", "0.0.0.0:9999", "--sync", "--remap", \
     "--", "pylsp", \
     "--", "clangd"]