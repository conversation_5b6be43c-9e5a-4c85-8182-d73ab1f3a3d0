use lsp_types::Position;

/// Extract code snippet for Python language.
/// Returns exactly 3 lines: one above error, error line, one below error.
/// For syntax errors, tries to include meaningful context.
pub fn extract_python_snippet(lines: &[&str], start: &Position, _end: &Position) -> String {
    let reported_line = start.line as usize;
    
    if reported_line >= lines.len() {
        return if !lines.is_empty() {
            lines[lines.len() - 1].to_string()
        } else {
            "".to_string()
        };
    }
    
    // For syntax errors, the actual problematic line might be before the reported line
    // Check if this looks like an unclosed parenthesis error
    let mut center_line = reported_line;
    if reported_line > 0 {
        let prev_line = lines[reported_line - 1];
        // If the previous line has an unclosed parenthesis, that's likely the actual error line
        if prev_line.contains('(') && !prev_line.contains(')') {
            center_line = reported_line - 1;
        }
    }
    
    let mut result_lines = Vec::new();
    
    // Add the line above the center line
    if center_line > 0 {
        let above_line = lines[center_line - 1];
        if !above_line.trim().is_empty() {
            result_lines.push(above_line);
        }
    }
    
    // Add the center line (always include)
    result_lines.push(lines[center_line]);
    
    // Add the line below the center line
    if center_line + 1 < lines.len() {
        let below_line = lines[center_line + 1];
        if !below_line.trim().is_empty() {
            result_lines.push(below_line);
        }
    }
    
    result_lines.join("\n")
}