use lsp_types::{Diagnostic, Position};
use super::pylang;
use super::clang;

/// Extract a code snippet from file lines based on the diagnostic range and language.
pub fn extract_code_snippet(lines: &[&str], start: &Position, end: &Position, language_id: Option<&str>) -> String {
    // Use language-specific extraction if language is provided
    if let Some(lang) = language_id {
        match lang {
            "python" => return pylang::extract_python_snippet(lines, start, end),
            "c" | "cpp" | "c++" => return clang::extract_c_snippet(lines, start, end),
            _ => {} // Fall through to default extraction
        }
    }
    
    // Extract 3 lines: one above error, error line, one below error
    // Ensure surrounding lines are not empty
    extract_three_line_snippet(lines, start)
}

/// Extract exactly 3 lines around the error: one above, error line, one below.
/// Ensures that surrounding lines are not empty.
fn extract_three_line_snippet(lines: &[&str], start: &Position) -> String {
    let error_line = start.line as usize;
    
    if error_line >= lines.len() {
        return if !lines.is_empty() {
            lines[lines.len() - 1].to_string()
        } else {
            "".to_string()
        };
    }
    
    let mut result_lines = Vec::new();
    
    // Add the line above if it exists (always include unless completely empty)
    if error_line > 0 {
        let above_line = lines[error_line - 1];
        // Include the line above unless it's completely empty (only whitespace)
        if !above_line.trim().is_empty() {
            result_lines.push(above_line);
        }
    }
    
    // Add the error line (always include)
    result_lines.push(lines[error_line]);
    
    // Add the line below if it exists (always include unless completely empty)
    if error_line + 1 < lines.len() {
        let below_line = lines[error_line + 1];
        // Include the line below unless it's completely empty (only whitespace)
        if !below_line.trim().is_empty() {
            result_lines.push(below_line);
        }
    }
    
    result_lines.join("\n")
}

/// Transform diagnostics into custom JSON objects with code snippets.
pub fn transform_diagnostics(
    diagnostics: &[Diagnostic],
    lines: &[&str],
    language_id: Option<&str>,
) -> Vec<serde_json::Value> {
    diagnostics
        .iter()
        .map(|diag| {
            let snippet = extract_code_snippet(lines, &diag.range.start, &diag.range.end, language_id);
            let mut obj = serde_json::json!({
                "code_snippet": snippet,
                "range": diag.range,
                "message": diag.message,
                "severity": diag.severity
            });
            if let Some(code) = &diag.code {
                obj["code"] = serde_json::to_value(code).unwrap_or(serde_json::Value::Null);
            }
            obj
        })
        .collect()
}
