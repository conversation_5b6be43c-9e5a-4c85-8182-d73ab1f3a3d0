use lsp_types::Position;

/// Extract code snippet for C/C++ language.
/// Returns exactly 3 lines: one above error, error line, one below error.
/// Ensures that surrounding lines are not empty.
pub fn extract_c_snippet(lines: &[&str], start: &Position, _end: &Position) -> String {
    let error_line = start.line as usize;
    
    if error_line >= lines.len() {
        return if !lines.is_empty() {
            lines[lines.len() - 1].to_string()
        } else {
            "".to_string()
        };
    }
    
    let mut result_lines = Vec::new();
    
    // Add the line above if it exists and is not empty
    if error_line > 0 {
        let above_line = lines[error_line - 1];
        if !above_line.trim().is_empty() {
            result_lines.push(above_line);
        }
    }
    
    // Add the error line
    result_lines.push(lines[error_line]);
    
    // Add the line below if it exists and is not empty
    if error_line + 1 < lines.len() {
        let below_line = lines[error_line + 1];
        if !below_line.trim().is_empty() {
            result_lines.push(below_line);
        }
    }
    
    result_lines.join("\n")
}