# lsp-ws-proxy

WebSocket proxy for Language Servers.

## Usage

```
$ lsp-ws-proxy --help

Usage: lsp-ws-proxy [-l <listen>] [-s] [-r] [-v]

Start WebSocket proxy for the LSP Server.
Anything after the option delimiter is used to start the server.

Multiple servers can be registered by separating each with an option delimiter,
and using the query parameter `name` to specify the command name on connection.
If no query parameter is present, the first one is started.

Examples:
  lsp-ws-proxy -- rust-analyzer
  lsp-ws-proxy -- typescript-language-server --stdio
  lsp-ws-proxy -- pylsp
  lsp-ws-proxy -- clangd
  lsp-ws-proxy --listen 8888 -- rust-analyzer
  lsp-ws-proxy --listen 0.0.0.0:8888 -- rust-analyzer
  # Register multiple servers.
  # Choose the server with query parameter `name` when connecting.
  lsp-ws-proxy --listen 9999 --sync --remap \
    -- typescript-language-server --stdio \
    -- css-languageserver --stdio \
    -- html-languageserver --stdio

Options:
  -l, --listen      address or port to listen on (default: 0.0.0.0:9999)
  -s, --sync        write text document to disk on save, and enable `/files`
                    endpoint
  -r, --remap       remap relative uri (source://)
  -v, --version     show version and exit
  --help            display usage information
```

## Why?

Remote Language Server is necessary when it's not possible to run the server next to the client.

For example, this can be used to let in-browser editors like [CodeMirror][codemirror] and [Monaco][monaco] to use any Language Servers.
See [qualified/lsps] for an example of using proxied [Rust Analyzer][rust-analyzer] with CodeMirror.

## Features

- [x] Proxy messages
- [x] Synchronize files
- [x] Manipulate remote files with `POST /files`
- [x] Remap relative `DocumentUri` (`source://`)
- [x] **Custom diagnostic transformation** - Transform LSP `publishDiagnostics` responses into simplified format
- [x] **Language-specific code snippet extraction** - Extract meaningful code snippets based on programming language:
  - **Python**: Extract entire function containing the error, or whole code if error is outside functions
  - **C/C++**: Extract function bounded by braces containing the error, or whole code if error is outside functions
  - **Other languages**: Fallback to character-range-based extraction
- [x] **File text caching** - Cache file contents from `didOpen` notifications for snippet extraction
- [x] **Error categorization** - Distinguish different error types in diagnostic messages
- [x] **Robust range handling** - Handle out-of-bounds diagnostic ranges gracefully

## Build and run the Docker to test the project:
**Build Docker image**
```sh
docker build --no-cache -t lsp-ws-proxy .
```
- Then Run container:
**Run Container**
```sh
docker run -p 9999:9999 lsp-ws-proxy
```

- Or pull the image:
**Pull image**
```sh
docker pull dinhminh123/lsp-ws-proxy:latest
```
- Run the image
```sh
docker run -p 9999:9999 dinhminh123/lsp-ws-proxy:latest
```

## Custom Diagnostic Format

The proxy transforms standard LSP `textDocument/publishDiagnostics` messages into a simplified format:

**Initialize to LSP Proxy server**
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "initialize",
  "params": {
    "processId": null,
    "rootUri": null,
    "capabilities": {}
  }
}
```

**Open C File with Function Error:**
```json
{
  "jsonrpc": "2.0",
  "method": "textDocument/didOpen",
  "params": {
    "textDocument": {
      "uri": "file:///C:/test/sample.c",
      "languageId": "c",
      "version": 1,
      "text": "#include <stdio.h>\n\nint add(int a, int b) {\n    int result = a + b;\n    printf(\"Result: %d\\n\", result)\n    return result;\n}\n\nint main() {\n    int x = 5;\n    int y = 3;\n    int sum = add(x, y);\n    return 0;\n}"
    }
  }
}
```

**Output (Custom Format):**
```json
{
    "diagnostics": [
        {
            "code": "expected_semi_after_expr",
            "code_snippet": "int add(int a, int b) {\n    int result = a + b;\n    printf(\"Result: %d\\n\", result)\n    return result;\n}",
            "message": "Expected ';' after expression (fix available)",
            "range": {
                "end": {
                    "character": 10,
                    "line": 5
                },
                "start": {
                    "character": 4,
                    "line": 5
                }
            },
            "severity": 1
        }
    ]
}
```

## Language Support

The proxy includes enhanced support for:

- **Python** (`pylsp`) - Function-level code extraction
- **C/C++** (`clangd`) - Function-level code extraction with brace matching
- **JavaScript/TypeScript** - Fallback extraction
- **Other LSP servers** - Generic diagnostic forwarding with fallback extraction

## Testing

You can test the proxy with various LSP servers Run this command:

```bash
# Python and Clangd Language Server
cargo run -- -- pylsp -- clangd    
```

Connect via WebSocket to `ws://localhost:9999/` and send LSP messages to see the custom diagnostic format in action.

[codemirror]: https://codemirror.net/
[monaco]: https://microsoft.github.io/monaco-editor/
[qualified/lsps]: https://github.com/qualified/lsps
[rust-analyzer]: https://github.com/rust-analyzer/rust-analyzer
