[package]
name = "lsp-ws-proxy"
version = "0.9.0-rc.4"
authors = ["kazk <<EMAIL>>"]
edition = "2018"
license = "MIT"
description = "WebSocketify any Language Server"
homepage = "https://github.com/qualified/lsp-ws-proxy"
repository = "https://github.com/qualified/lsp-ws-proxy"
readme = "README.md"
keywords = ["lsp", "websocket"]
categories = ["command-line-utilities"]

[dependencies]
argh = "0.1.4"
bytes = "1.0.1"
futures-util = "0.3.15"
lsp-types = "0.89.2"
nom = { version = "6.1.2", default-features = false, features = ["std"] }
serde = { version = "1.0.126", features = ["derive"] }
serde_json = "1.0.64"
url = "2.2.2"

tokio = { version = "1.6.1", features = ["fs", "process", "macros", "rt", "rt-multi-thread", "time"] }
tokio-util = { version = "0.6.7", features = ["codec"] }
warp = { git = "https://github.com/kazk/warp", branch = "permessage-deflate", default-features = false, features = ["websocket"] }

tracing = "0.1.26"
tracing-subscriber = { version = "0.2", features = ["env-filter", "regex"] }
regex = { version = "1", features = ["unicode-case"] }
lazy_static = "1.4"
thiserror = "1.0.26"

[dev-dependencies]

[profile.release]
# Turn Link Time Optimization on to reduce the binary size.
# See https://doc.rust-lang.org/cargo/reference/profiles.html#lto
lto = true

# Strip symbols from the final build (nightly)
# See https://github.com/rust-lang/cargo/issues/3483#issuecomment-631395566
# strip = "symbols"
